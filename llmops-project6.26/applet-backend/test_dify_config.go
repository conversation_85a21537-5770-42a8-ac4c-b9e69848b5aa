package main

import (
	"fmt"
	"os"
	"path/filepath"

	"transwarp.io/applied-ai/applet-backend/conf"
)

func main() {
	// 设置配置文件路径
	os.Setenv("ETC_PATH", filepath.Join(".", "etc"))
	
	// 初始化配置
	conf.Init()
	
	// 检查Dify配置是否正确加载
	difyConfig := conf.Config.DifyConfig
	
	fmt.Printf("Dify配置加载结果:\n")
	fmt.Printf("API Base URL: %s\n", difyConfig.APIBaseURL)
	fmt.Printf("Workflow Base URL: %s\n", difyConfig.WorkflowBaseURL)
	fmt.Printf("Chatbot Base URL: %s\n", difyConfig.ChatbotBaseURL)
	fmt.Printf("Bearer Token: %s\n", difyConfig.BearerToken[:50]+"...")
	fmt.Printf("Use Proxy: %t\n", difyConfig.UseProxy)
	fmt.Printf("Proxy URL: %s\n", difyConfig.ProxyURL)
	
	fmt.Println("\n配置加载成功！")
}
